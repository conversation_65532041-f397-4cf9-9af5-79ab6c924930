/**
 * SQLite到PostgreSQL完整数据迁移脚本
 * 一次性迁移所有数据，不保留SQLite
 */

require('dotenv').config({ path: '.env.postgresql' });
const Database = require('better-sqlite3');
const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

class DataMigrator {
    constructor() {
        this.sqliteDb = null;
        this.pgPool = null;
        this.migrationStats = {
            totalTables: 0,
            migratedTables: 0,
            totalRecords: 0,
            migratedRecords: 0,
            errors: []
        };
    }

    /**
     * 初始化数据库连接
     */
    async init() {
        console.log('🔧 初始化数据库连接...');
        
        // SQLite连接
        const sqlitePath = path.join(__dirname, '../backend/database/application_system.db');
        if (!fs.existsSync(sqlitePath)) {
            throw new Error(`SQLite数据库文件不存在: ${sqlitePath}`);
        }
        
        this.sqliteDb = new Database(sqlitePath, { readonly: true });
        console.log('✅ SQLite连接成功');
        
        // PostgreSQL连接
        this.pgPool = new Pool({
            user: process.env.PG_USER || 'postgres',
            host: process.env.PG_HOST || 'localhost',
            database: process.env.PG_DATABASE || 'makrite_managementsystem',
            password: process.env.PG_PASSWORD,
            port: process.env.PG_PORT || 5432,
            max: 10,
            idleTimeoutMillis: 30000,
            connectionTimeoutMillis: 5000,
        });
        
        // 测试PostgreSQL连接
        const client = await this.pgPool.connect();
        await client.query('SELECT NOW()');
        client.release();
        console.log('✅ PostgreSQL连接成功');
    }

    /**
     * 获取所有表名和记录数
     */
    getTableInfo() {
        console.log('📊 分析表结构和数据量...');
        
        const tables = this.sqliteDb.prepare(`
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name NOT LIKE 'sqlite_%'
            ORDER BY name
        `).all();
        
        const tableInfo = tables.map(table => {
            const countResult = this.sqliteDb.prepare(`SELECT COUNT(*) as count FROM ${table.name}`).get();
            return {
                name: table.name,
                recordCount: countResult.count
            };
        });
        
        this.migrationStats.totalTables = tableInfo.length;
        this.migrationStats.totalRecords = tableInfo.reduce((sum, table) => sum + table.recordCount, 0);
        
        console.log(`📋 发现 ${tableInfo.length} 个表，总计 ${this.migrationStats.totalRecords} 条记录`);
        tableInfo.forEach(table => {
            console.log(`   ${table.name}: ${table.recordCount} 条记录`);
        });
        
        return tableInfo;
    }

    /**
     * 迁移单个表的数据
     */
    async migrateTable(tableName) {
        console.log(`🔄 迁移表: ${tableName}`);
        
        try {
            // 获取SQLite表数据
            const rows = this.sqliteDb.prepare(`SELECT * FROM ${tableName}`).all();
            
            if (rows.length === 0) {
                console.log(`   ℹ️ 表 ${tableName} 为空，跳过迁移`);
                return;
            }
            
            // 获取列信息
            const columns = Object.keys(rows[0]);
            
            // 清空PostgreSQL表（如果有数据）
            await this.pgPool.query(`DELETE FROM ${tableName}`);
            
            // 批量插入数据
            const batchSize = 100;
            let insertedCount = 0;
            
            for (let i = 0; i < rows.length; i += batchSize) {
                const batch = rows.slice(i, i + batchSize);
                await this.insertBatch(tableName, columns, batch);
                insertedCount += batch.length;
                
                // 显示进度
                const progress = Math.round((insertedCount / rows.length) * 100);
                process.stdout.write(`\r   📈 进度: ${insertedCount}/${rows.length} (${progress}%)`);
            }
            
            console.log(`\n   ✅ 表 ${tableName} 迁移完成: ${insertedCount} 条记录`);
            this.migrationStats.migratedRecords += insertedCount;
            this.migrationStats.migratedTables++;
            
        } catch (error) {
            console.error(`\n   ❌ 表 ${tableName} 迁移失败:`, error.message);
            this.migrationStats.errors.push({
                table: tableName,
                error: error.message
            });
        }
    }

    /**
     * 批量插入数据
     */
    async insertBatch(tableName, columns, rows) {
        const values = rows.flatMap(row => columns.map(col => {
            let value = row[col];

            // 处理特殊数据类型
            if (value === null || value === undefined) {
                return null;
            }

            // 处理布尔值（SQLite用0/1，PostgreSQL用true/false）
            if (typeof value === 'number' && (value === 0 || value === 1)) {
                // 检查列名是否暗示布尔类型
                const booleanColumns = [
                    'active', 'has_signature', 'need_manager_approval', 'need_ceo_approval',
                    'auto_saved', 'is_built_in', 'is_primary', 'is_intelligent', 'enabled',
                    'is_default', 'is_active', 'is_deleted', 'is_completed', 'is_approved',
                    'is_public', 'is_template', 'is_system', 'is_locked'
                ];
                if (booleanColumns.includes(col.toLowerCase())) {
                    return value === 1;
                }
            }

            // 处理字符串中的布尔值
            if (typeof value === 'string') {
                if (value === 'true' || value === 'false') {
                    return value === 'true';
                }
            }

            return value;
        }));

        const query = `
            INSERT INTO ${tableName} (${columns.join(', ')})
            VALUES ${rows.map((_, index) => `(${columns.map((_, colIndex) => `$${index * columns.length + colIndex + 1}`).join(', ')})`).join(', ')}
            ON CONFLICT DO NOTHING
        `;

        await this.pgPool.query(query, values);
    }

    /**
     * 验证数据迁移完整性
     */
    async validateMigration() {
        console.log('\n🔍 验证数据迁移完整性...');
        
        const tables = this.sqliteDb.prepare(`
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name NOT LIKE 'sqlite_%'
        `).all();
        
        let validationPassed = true;
        
        for (const table of tables) {
            const sqliteCount = this.sqliteDb.prepare(`SELECT COUNT(*) as count FROM ${table.name}`).get().count;
            const pgResult = await this.pgPool.query(`SELECT COUNT(*) as count FROM ${table.name}`);
            const pgCount = parseInt(pgResult.rows[0].count);
            
            if (sqliteCount === pgCount) {
                console.log(`   ✅ ${table.name}: ${sqliteCount} = ${pgCount}`);
            } else {
                console.log(`   ❌ ${table.name}: SQLite(${sqliteCount}) ≠ PostgreSQL(${pgCount})`);
                validationPassed = false;
            }
        }
        
        return validationPassed;
    }

    /**
     * 执行完整迁移
     */
    async migrate() {
        try {
            await this.init();
            
            const tableInfo = this.getTableInfo();
            
            console.log('\n🚀 开始数据迁移...');
            const startTime = Date.now();
            
            // 按依赖顺序迁移表（先迁移被引用的表）
            const migrationOrder = [
                // 基础表（无外键依赖）
                'users', 'departments', 'permission_templates', 'products', 'equipment',
                'factories', 'migrations', 'quality_report_numbers', 'inventory_tags',

                // 依赖基础表的表
                'production_processes', 'equipment_capacity', 'equipment_health',
                'equipment_health_history', 'equipment_maintenance',

                // 应用相关表
                'applications', 'application_attachments', 'application_drafts', 'approval_history',

                // 排程相关表
                'schedules', 'schedule_progress', 'schedule_plans',

                // 设备和操作员关联表
                'equipment_capabilities', 'operator_skills', 'equipment_operators',

                // 质量管理表
                'quality_reports', 'quality_report_files',

                // 文件管理表
                'file_management_customers', 'file_management_products', 'file_management_files', 'file_management_attachments',

                // 仓库管理表
                'warehouse_materials', 'warehouse_finished_products', 'warehouse_inventory_transactions', 'warehouse_qrcodes',

                // 库存管理表
                'inventory_items', 'inventory_item_tags', 'inventory_operations', 'inventory_records', 'inventory_label_templates'
            ];
            
            // 迁移有序表
            for (const tableName of migrationOrder) {
                if (tableInfo.find(t => t.name === tableName)) {
                    await this.migrateTable(tableName);
                }
            }
            
            // 迁移剩余表
            const remainingTables = tableInfo.filter(t => !migrationOrder.includes(t.name));
            for (const table of remainingTables) {
                await this.migrateTable(table.name);
            }
            
            const endTime = Date.now();
            const duration = Math.round((endTime - startTime) / 1000);
            
            console.log('\n📊 迁移统计:');
            console.log(`   总表数: ${this.migrationStats.totalTables}`);
            console.log(`   成功迁移: ${this.migrationStats.migratedTables}`);
            console.log(`   总记录数: ${this.migrationStats.totalRecords}`);
            console.log(`   成功迁移: ${this.migrationStats.migratedRecords}`);
            console.log(`   耗时: ${duration} 秒`);
            
            if (this.migrationStats.errors.length > 0) {
                console.log('\n❌ 迁移错误:');
                this.migrationStats.errors.forEach(error => {
                    console.log(`   ${error.table}: ${error.error}`);
                });
            }
            
            // 验证迁移完整性
            const validationPassed = await this.validateMigration();
            
            if (validationPassed && this.migrationStats.errors.length === 0) {
                console.log('\n🎉 数据迁移完全成功！');
                return true;
            } else {
                console.log('\n⚠️ 数据迁移完成，但存在问题，请检查上述错误');
                return false;
            }
            
        } catch (error) {
            console.error('\n❌ 迁移过程中发生严重错误:', error);
            return false;
        } finally {
            if (this.sqliteDb) {
                this.sqliteDb.close();
            }
            if (this.pgPool) {
                await this.pgPool.end();
            }
        }
    }
}

// 执行迁移
if (require.main === module) {
    const migrator = new DataMigrator();
    migrator.migrate()
        .then(success => {
            process.exit(success ? 0 : 1);
        })
        .catch(error => {
            console.error('❌ 迁移执行失败:', error);
            process.exit(1);
        });
}

module.exports = DataMigrator;

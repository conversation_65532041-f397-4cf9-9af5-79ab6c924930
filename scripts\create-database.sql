-- PostgreSQL database creation script
-- Create project database: makrite_managementsystem

-- Create database
CREATE DATABASE makrite_managementsystem
    WITH
    OWNER = postgres
    ENCODING = 'UTF8'
    LC_COLLATE = 'C'
    LC_CTYPE = 'C'
    TABLESPACE = pg_default
    CONNECTION LIMIT = -1
    IS_TEMPLATE = False;

-- Connect to new database
\c makrite_managementsystem;

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";  -- UUID generation
CREATE EXTENSION IF NOT EXISTS "pg_trgm";    -- Full text search support
CREATE EXTENSION IF NOT EXISTS "btree_gin";  -- GIN index support

-- Set timezone
SET timezone = 'Asia/Shanghai';

-- Create application user (optional, for production environment)
-- CREATE USER makrite_app WITH PASSWORD 'your_secure_password';
-- GRANT CONNECT ON DATABASE makrite_managementsystem TO makrite_app;
-- GRA<PERSON> USAGE ON SCHEMA public TO makrite_app;
-- GRANT CREATE ON SCHEMA public TO makrite_app;

COMMENT ON DATABASE makrite_managementsystem IS 'Makrite Intelligent Enterprise Management System Database';

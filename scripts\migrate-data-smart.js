/**
 * 智能数据迁移脚本
 * 处理数据类型转换和外键约束问题
 */

require('dotenv').config({ path: '.env.postgresql' });
const Database = require('better-sqlite3');
const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

class SmartDataMigrator {
    constructor() {
        this.sqliteDb = null;
        this.pgPool = null;
        this.migrationStats = {
            totalTables: 0,
            migratedTables: 0,
            totalRecords: 0,
            migratedRecords: 0,
            errors: []
        };
    }

    async init() {
        console.log('🔧 初始化数据库连接...');
        
        const sqlitePath = path.join(__dirname, '../backend/database/application_system.db');
        this.sqliteDb = new Database(sqlitePath, { readonly: true });
        console.log('✅ SQLite连接成功');
        
        this.pgPool = new Pool({
            user: process.env.PG_USER || 'postgres',
            host: process.env.PG_HOST || 'localhost',
            database: process.env.PG_DATABASE || 'makrite_managementsystem',
            password: process.env.PG_PASSWORD,
            port: process.env.PG_PORT || 5432,
            max: 10,
        });
        
        const client = await this.pgPool.connect();
        await client.query('SELECT NOW()');
        client.release();
        console.log('✅ PostgreSQL连接成功');
    }

    /**
     * 检查PostgreSQL表是否存在
     */
    async tableExists(tableName) {
        const result = await this.pgPool.query(`
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = $1
            )
        `, [tableName]);
        return result.rows[0].exists;
    }

    /**
     * 获取PostgreSQL表的列信息
     */
    async getPostgreSQLColumns(tableName) {
        const result = await this.pgPool.query(`
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns
            WHERE table_schema = 'public' AND table_name = $1
            ORDER BY ordinal_position
        `, [tableName]);
        return result.rows;
    }

    /**
     * 智能数据类型转换
     */
    convertValue(value, columnName, dataType) {
        if (value === null || value === undefined) {
            return null;
        }

        // 布尔值处理
        const booleanColumns = [
            'active', 'has_signature', 'need_manager_approval', 'need_ceo_approval', 
            'auto_saved', 'is_built_in', 'is_primary', 'is_intelligent', 'enabled',
            'is_default', 'is_active', 'is_deleted', 'is_completed', 'is_approved',
            'is_public', 'is_template', 'is_system', 'is_locked'
        ];

        if (booleanColumns.includes(columnName.toLowerCase()) || dataType === 'boolean') {
            if (typeof value === 'number') {
                return value === 1;
            }
            if (typeof value === 'string') {
                return value.toLowerCase() === 'true' || value === '1';
            }
            return Boolean(value);
        }

        // 时间戳处理
        if (dataType === 'timestamp' || dataType === 'timestamp without time zone') {
            if (typeof value === 'string' && value.length > 0) {
                // 尝试解析时间字符串
                const date = new Date(value);
                return isNaN(date.getTime()) ? null : date.toISOString();
            }
        }

        // 数字处理
        if (dataType === 'integer' && typeof value === 'string') {
            const num = parseInt(value);
            return isNaN(num) ? null : num;
        }

        if (dataType === 'real' && typeof value === 'string') {
            const num = parseFloat(value);
            return isNaN(num) ? null : num;
        }

        return value;
    }

    /**
     * 迁移单个表
     */
    async migrateTable(tableName) {
        console.log(`🔄 迁移表: ${tableName}`);
        
        try {
            // 检查表是否存在
            const exists = await this.tableExists(tableName);
            if (!exists) {
                console.log(`   ⚠️ 表 ${tableName} 在PostgreSQL中不存在，跳过`);
                return;
            }

            // 获取SQLite数据
            const rows = this.sqliteDb.prepare(`SELECT * FROM ${tableName}`).all();
            
            if (rows.length === 0) {
                console.log(`   ℹ️ 表 ${tableName} 为空，跳过迁移`);
                return;
            }

            // 获取PostgreSQL列信息
            const pgColumns = await this.getPostgreSQLColumns(tableName);
            const pgColumnMap = {};
            pgColumns.forEach(col => {
                pgColumnMap[col.column_name] = col;
            });

            // 清空表
            await this.pgPool.query(`TRUNCATE TABLE ${tableName} RESTART IDENTITY CASCADE`);

            // 准备数据
            const columns = Object.keys(rows[0]).filter(col => pgColumnMap[col]);
            
            if (columns.length === 0) {
                console.log(`   ⚠️ 表 ${tableName} 没有匹配的列，跳过`);
                return;
            }

            // 批量插入
            const batchSize = 50;
            let insertedCount = 0;

            for (let i = 0; i < rows.length; i += batchSize) {
                const batch = rows.slice(i, i + batchSize);
                
                try {
                    await this.insertBatch(tableName, columns, batch, pgColumnMap);
                    insertedCount += batch.length;
                    
                    const progress = Math.round((insertedCount / rows.length) * 100);
                    process.stdout.write(`\r   📈 进度: ${insertedCount}/${rows.length} (${progress}%)`);
                } catch (batchError) {
                    console.log(`\n   ⚠️ 批次插入失败: ${batchError.message}`);
                    
                    // 尝试逐行插入
                    for (const row of batch) {
                        try {
                            await this.insertBatch(tableName, columns, [row], pgColumnMap);
                            insertedCount++;
                        } catch (rowError) {
                            console.log(`\n   ❌ 行插入失败: ${JSON.stringify(row)} - ${rowError.message}`);
                        }
                    }
                }
            }

            console.log(`\n   ✅ 表 ${tableName} 迁移完成: ${insertedCount} 条记录`);
            this.migrationStats.migratedRecords += insertedCount;
            this.migrationStats.migratedTables++;

        } catch (error) {
            console.error(`\n   ❌ 表 ${tableName} 迁移失败:`, error.message);
            this.migrationStats.errors.push({
                table: tableName,
                error: error.message
            });
        }
    }

    /**
     * 批量插入数据
     */
    async insertBatch(tableName, columns, rows, pgColumnMap) {
        const values = rows.flatMap(row => 
            columns.map(col => {
                const value = row[col];
                const pgColumn = pgColumnMap[col];
                return this.convertValue(value, col, pgColumn.data_type);
            })
        );

        const placeholders = rows.map((_, rowIndex) => 
            `(${columns.map((_, colIndex) => `$${rowIndex * columns.length + colIndex + 1}`).join(', ')})`
        ).join(', ');

        const query = `
            INSERT INTO ${tableName} (${columns.join(', ')})
            VALUES ${placeholders}
        `;

        await this.pgPool.query(query, values);
    }

    /**
     * 执行迁移
     */
    async migrate() {
        try {
            await this.init();

            console.log('📊 分析表结构...');
            const tables = this.sqliteDb.prepare(`
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name NOT LIKE 'sqlite_%'
                ORDER BY name
            `).all();

            this.migrationStats.totalTables = tables.length;
            console.log(`发现 ${tables.length} 个表`);

            console.log('\n🚀 开始数据迁移...');
            const startTime = Date.now();

            // 按优先级迁移表
            const priorityTables = [
                'users', 'departments', 'permission_templates', 'products', 'equipment',
                'factories', 'migrations', 'quality_report_numbers', 'inventory_tags'
            ];

            // 先迁移优先级表
            for (const tableName of priorityTables) {
                if (tables.find(t => t.name === tableName)) {
                    await this.migrateTable(tableName);
                }
            }

            // 再迁移其他表
            const remainingTables = tables.filter(t => !priorityTables.includes(t.name));
            for (const table of remainingTables) {
                await this.migrateTable(table.name);
            }

            const endTime = Date.now();
            const duration = Math.round((endTime - startTime) / 1000);

            console.log('\n📊 迁移统计:');
            console.log(`   总表数: ${this.migrationStats.totalTables}`);
            console.log(`   成功迁移: ${this.migrationStats.migratedTables}`);
            console.log(`   总记录数: ${this.migrationStats.migratedRecords}`);
            console.log(`   耗时: ${duration} 秒`);

            if (this.migrationStats.errors.length > 0) {
                console.log('\n⚠️ 迁移警告:');
                this.migrationStats.errors.forEach(error => {
                    console.log(`   ${error.table}: ${error.error}`);
                });
            }

            // 验证关键表
            await this.validateKeyTables();

            console.log('\n🎉 数据迁移完成！');
            return true;

        } catch (error) {
            console.error('\n❌ 迁移失败:', error);
            return false;
        } finally {
            if (this.sqliteDb) {
                this.sqliteDb.close();
            }
            if (this.pgPool) {
                await this.pgPool.end();
            }
        }
    }

    /**
     * 验证关键表的数据
     */
    async validateKeyTables() {
        console.log('\n🔍 验证关键表数据...');
        
        const keyTables = ['users', 'departments', 'products', 'equipment'];
        
        for (const tableName of keyTables) {
            try {
                const sqliteCount = this.sqliteDb.prepare(`SELECT COUNT(*) as count FROM ${tableName}`).get().count;
                const pgResult = await this.pgPool.query(`SELECT COUNT(*) as count FROM ${tableName}`);
                const pgCount = parseInt(pgResult.rows[0].count);
                
                if (sqliteCount === pgCount) {
                    console.log(`   ✅ ${tableName}: ${sqliteCount} = ${pgCount}`);
                } else {
                    console.log(`   ⚠️ ${tableName}: SQLite(${sqliteCount}) ≠ PostgreSQL(${pgCount})`);
                }
            } catch (error) {
                console.log(`   ❌ ${tableName}: 验证失败 - ${error.message}`);
            }
        }
    }
}

// 执行迁移
if (require.main === module) {
    const migrator = new SmartDataMigrator();
    migrator.migrate()
        .then(success => {
            process.exit(success ? 0 : 1);
        })
        .catch(error => {
            console.error('❌ 迁移执行失败:', error);
            process.exit(1);
        });
}

module.exports = SmartDataMigrator;

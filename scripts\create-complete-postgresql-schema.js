/**
 * 创建完整的PostgreSQL表结构
 * 基于SQLite数据库自动生成所有表
 */

require('dotenv').config({ path: '.env.postgresql' });
const Database = require('better-sqlite3');
const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

class SchemaCreator {
    constructor() {
        this.sqliteDb = null;
        this.pgPool = null;
    }

    async init() {
        console.log('🔧 初始化数据库连接...');
        
        // SQLite连接
        const sqlitePath = path.join(__dirname, '../backend/database/application_system.db');
        this.sqliteDb = new Database(sqlitePath, { readonly: true });
        console.log('✅ SQLite连接成功');
        
        // PostgreSQL连接
        this.pgPool = new Pool({
            user: process.env.PG_USER || 'postgres',
            host: process.env.PG_HOST || 'localhost',
            database: process.env.PG_DATABASE || 'makrite_managementsystem',
            password: process.env.PG_PASSWORD,
            port: process.env.PG_PORT || 5432,
        });
        
        const client = await this.pgPool.connect();
        await client.query('SELECT NOW()');
        client.release();
        console.log('✅ PostgreSQL连接成功');
    }

    /**
     * 获取SQLite表结构
     */
    getTableSchema(tableName) {
        const columns = this.sqliteDb.prepare(`PRAGMA table_info(${tableName})`).all();
        const foreignKeys = this.sqliteDb.prepare(`PRAGMA foreign_key_list(${tableName})`).all();
        
        return {
            columns: columns.map(col => ({
                name: col.name,
                type: this.mapSQLiteTypeToPostgreSQL(col.type),
                nullable: !col.notnull,
                defaultValue: col.dflt_value,
                primaryKey: col.pk === 1
            })),
            foreignKeys: foreignKeys.map(fk => ({
                column: fk.from,
                referencedTable: fk.table,
                referencedColumn: fk.to,
                onDelete: fk.on_delete,
                onUpdate: fk.on_update
            }))
        };
    }

    /**
     * 映射SQLite数据类型到PostgreSQL
     */
    mapSQLiteTypeToPostgreSQL(sqliteType) {
        const typeMap = {
            'TEXT': 'TEXT',
            'INTEGER': 'INTEGER',
            'REAL': 'REAL',
            'BLOB': 'BYTEA',
            'NUMERIC': 'NUMERIC',
            'VARCHAR': 'VARCHAR',
            'CHAR': 'CHAR',
            'BOOLEAN': 'BOOLEAN',
            'DATE': 'DATE',
            'DATETIME': 'TIMESTAMP',
            'TIME': 'TIME'
        };

        const upperType = sqliteType.toUpperCase();
        
        // 处理带长度的类型
        if (upperType.includes('(')) {
            const baseType = upperType.split('(')[0];
            return typeMap[baseType] ? sqliteType.replace(baseType, typeMap[baseType]) : sqliteType;
        }
        
        return typeMap[upperType] || sqliteType;
    }

    /**
     * 生成CREATE TABLE语句
     */
    generateCreateTableSQL(tableName, schema) {
        let sql = `CREATE TABLE IF NOT EXISTS ${tableName} (\n`;
        
        // 列定义
        const columnDefs = schema.columns.map(col => {
            let def = `    ${col.name} ${col.type}`;
            
            // 处理AUTOINCREMENT -> SERIAL
            if (col.primaryKey && col.type === 'INTEGER' && tableName.includes('approval_history')) {
                def = `    ${col.name} SERIAL`;
            }
            
            if (col.primaryKey) {
                def += ' PRIMARY KEY';
            }
            
            if (!col.nullable && !col.primaryKey) {
                def += ' NOT NULL';
            }
            
            if (col.defaultValue !== null && col.defaultValue !== undefined) {
                // 处理默认值
                let defaultVal = col.defaultValue;
                if (defaultVal === 'CURRENT_TIMESTAMP') {
                    defaultVal = 'CURRENT_TIMESTAMP';
                } else if (typeof defaultVal === 'string' && !defaultVal.startsWith("'")) {
                    defaultVal = `'${defaultVal}'`;
                }
                def += ` DEFAULT ${defaultVal}`;
            }
            
            return def;
        });
        
        sql += columnDefs.join(',\n');
        
        // 外键约束
        if (schema.foreignKeys.length > 0) {
            sql += ',\n';
            const fkDefs = schema.foreignKeys.map(fk => {
                let fkDef = `    FOREIGN KEY (${fk.column}) REFERENCES ${fk.referencedTable}(${fk.referencedColumn})`;
                if (fk.onDelete && fk.onDelete !== 'NO ACTION') {
                    fkDef += ` ON DELETE ${fk.onDelete}`;
                }
                if (fk.onUpdate && fk.onUpdate !== 'NO ACTION') {
                    fkDef += ` ON UPDATE ${fk.onUpdate}`;
                }
                return fkDef;
            });
            sql += fkDefs.join(',\n');
        }
        
        sql += '\n);';
        
        return sql;
    }

    /**
     * 创建所有表
     */
    async createAllTables() {
        console.log('📋 获取所有表名...');
        
        const tables = this.sqliteDb.prepare(`
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name NOT LIKE 'sqlite_%'
            ORDER BY name
        `).all();
        
        console.log(`发现 ${tables.length} 个表`);
        
        // 先删除所有外键约束，避免依赖问题
        console.log('🗑️ 清理现有表结构...');
        for (const table of tables) {
            try {
                await this.pgPool.query(`DROP TABLE IF EXISTS ${table.name} CASCADE`);
            } catch (error) {
                // 忽略删除错误
            }
        }
        
        console.log('🏗️ 创建表结构...');
        
        // 第一轮：创建所有表（不包含外键）
        for (const table of tables) {
            try {
                const schema = this.getTableSchema(table.name);
                
                // 生成不包含外键的CREATE TABLE语句
                let sql = `CREATE TABLE IF NOT EXISTS ${table.name} (\n`;
                
                const columnDefs = schema.columns.map(col => {
                    let def = `    ${col.name} ${col.type}`;
                    
                    // 处理AUTOINCREMENT -> SERIAL
                    if (col.primaryKey && col.type === 'INTEGER' && 
                        (table.name === 'approval_history' || table.name === 'schedule_progress')) {
                        def = `    ${col.name} SERIAL`;
                    }
                    
                    if (col.primaryKey) {
                        def += ' PRIMARY KEY';
                    }
                    
                    if (!col.nullable && !col.primaryKey) {
                        def += ' NOT NULL';
                    }
                    
                    if (col.defaultValue !== null && col.defaultValue !== undefined) {
                        let defaultVal = col.defaultValue;
                        if (defaultVal === 'CURRENT_TIMESTAMP') {
                            defaultVal = 'CURRENT_TIMESTAMP';
                        } else if (typeof defaultVal === 'string' && !defaultVal.startsWith("'") && !defaultVal.startsWith('[')) {
                            defaultVal = `'${defaultVal}'`;
                        }
                        def += ` DEFAULT ${defaultVal}`;
                    }
                    
                    return def;
                });
                
                sql += columnDefs.join(',\n');
                sql += '\n);';
                
                await this.pgPool.query(sql);
                console.log(`   ✅ 创建表: ${table.name}`);
                
            } catch (error) {
                console.error(`   ❌ 创建表失败 ${table.name}:`, error.message);
            }
        }
        
        // 第二轮：添加外键约束（如果需要）
        console.log('🔗 添加外键约束...');
        for (const table of tables) {
            try {
                const schema = this.getTableSchema(table.name);
                
                for (const fk of schema.foreignKeys) {
                    // 检查引用的表是否存在
                    const refTableExists = tables.some(t => t.name === fk.referencedTable);
                    if (refTableExists) {
                        const constraintName = `fk_${table.name}_${fk.column}`;
                        let fkSql = `ALTER TABLE ${table.name} ADD CONSTRAINT ${constraintName} `;
                        fkSql += `FOREIGN KEY (${fk.column}) REFERENCES ${fk.referencedTable}(${fk.referencedColumn})`;
                        
                        if (fk.onDelete && fk.onDelete !== 'NO ACTION') {
                            fkSql += ` ON DELETE ${fk.onDelete}`;
                        }
                        if (fk.onUpdate && fk.onUpdate !== 'NO ACTION') {
                            fkSql += ` ON UPDATE ${fk.onUpdate}`;
                        }
                        
                        try {
                            await this.pgPool.query(fkSql);
                            console.log(`   ✅ 添加外键: ${table.name}.${fk.column} -> ${fk.referencedTable}.${fk.referencedColumn}`);
                        } catch (fkError) {
                            console.log(`   ⚠️ 跳过外键: ${table.name}.${fk.column} (${fkError.message.split('\n')[0]})`);
                        }
                    }
                }
                
            } catch (error) {
                console.error(`   ❌ 添加外键失败 ${table.name}:`, error.message);
            }
        }
        
        console.log('🎉 表结构创建完成！');
    }

    async close() {
        if (this.sqliteDb) {
            this.sqliteDb.close();
        }
        if (this.pgPool) {
            await this.pgPool.end();
        }
    }
}

// 执行创建
if (require.main === module) {
    const creator = new SchemaCreator();
    creator.init()
        .then(() => creator.createAllTables())
        .then(() => {
            console.log('✅ 所有表结构创建成功！');
            return creator.close();
        })
        .then(() => process.exit(0))
        .catch(error => {
            console.error('❌ 创建失败:', error);
            creator.close().then(() => process.exit(1));
        });
}

module.exports = SchemaCreator;

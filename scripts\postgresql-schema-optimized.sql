-- PostgreSQL Optimized Schema for Makrite Management System
-- Converted from SQLite with PostgreSQL-specific optimizations

-- Connect to the database
\c makrite_managementsystem;

-- Set timezone
SET timezone = 'Asia/Shanghai';

-- Create users table first (referenced by other tables)
CREATE TABLE IF NOT EXISTS users (
    id TEXT PRIMARY KEY,
    usercode TEXT UNIQUE NOT NULL,
    username TEXT NOT NULL,
    password TEXT NOT NULL,
    role TEXT NOT NULL,
    department TEXT,
    email TEXT,
    active INTEGER DEFAULT 1,
    permissions TEXT DEFAULT '[]',
    has_signature INTEGER DEFAULT 0,
    signature_path TEXT,
    signature_base64 TEXT,
    last_login_at TIMESTAMP,
    last_active_at TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create applications table
CREATE TABLE IF NOT EXISTS applications (
    id TEXT PRIMARY KEY,
    application_number TEXT UNIQUE NOT NULL,
    user_id TEXT NOT NULL,
    applicant TEXT NOT NULL,
    department TEXT,
    date TEXT NOT NULL,
    content TEXT NOT NULL,
    amount TEXT,
    priority TEXT DEFAULT 'normal',
    type TEXT DEFAULT 'standard',
    status TEXT DEFAULT 'pending',
    current_stage TEXT,
    need_manager_approval INTEGER DEFAULT 0,
    selected_factory_managers TEXT DEFAULT '[]',
    pdf_path TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    selected_managers TEXT DEFAULT '[]',
    need_ceo_approval INTEGER DEFAULT 1,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Create application_attachments table
CREATE TABLE IF NOT EXISTS application_attachments (
    id TEXT PRIMARY KEY,
    application_id TEXT NOT NULL,
    name TEXT NOT NULL,
    path TEXT NOT NULL,
    filename TEXT,
    type TEXT,
    size INTEGER,
    uploaded_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (application_id) REFERENCES applications(id) ON DELETE CASCADE
);

-- Create application_drafts table
CREATE TABLE IF NOT EXISTS application_drafts (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    title TEXT,
    type TEXT DEFAULT 'standard',
    draft_data TEXT NOT NULL,
    auto_saved INTEGER DEFAULT 0,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create approval_history table
CREATE TABLE IF NOT EXISTS approval_history (
    id SERIAL PRIMARY KEY,
    application_id TEXT NOT NULL,
    stage TEXT NOT NULL,
    approver_id TEXT NOT NULL,
    approver_name TEXT NOT NULL,
    approver_role TEXT NOT NULL,
    action TEXT NOT NULL,
    comment TEXT,
    signature_path TEXT,
    timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (application_id) REFERENCES applications(id) ON DELETE CASCADE,
    FOREIGN KEY (approver_id) REFERENCES users(id)
);

-- Create permission_templates table
CREATE TABLE IF NOT EXISTS permission_templates (
    id TEXT PRIMARY KEY,
    name TEXT UNIQUE NOT NULL,
    description TEXT,
    permissions TEXT NOT NULL DEFAULT '[]',
    is_built_in INTEGER DEFAULT 0,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create departments table
CREATE TABLE IF NOT EXISTS departments (
    id TEXT PRIMARY KEY,
    name TEXT UNIQUE NOT NULL,
    description TEXT,
    active INTEGER DEFAULT 1,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create products table
CREATE TABLE IF NOT EXISTS products (
    id TEXT PRIMARY KEY,
    code TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL,
    category TEXT,
    specifications TEXT DEFAULT '{}',
    unit TEXT DEFAULT 'pcs',
    standard_time INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create equipment table
CREATE TABLE IF NOT EXISTS equipment (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    type TEXT NOT NULL,
    model TEXT,
    status TEXT DEFAULT 'active',
    location TEXT,
    capacity_per_hour INTEGER DEFAULT 0,
    efficiency_factor REAL DEFAULT 1.0,
    maintenance_interval INTEGER DEFAULT 168,
    last_maintenance TIMESTAMP,
    next_maintenance TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create production_processes table
CREATE TABLE IF NOT EXISTS production_processes (
    id TEXT PRIMARY KEY,
    product_id TEXT NOT NULL,
    process_name TEXT NOT NULL,
    sequence_order INTEGER NOT NULL,
    standard_time INTEGER NOT NULL,
    required_equipment_type TEXT,
    skill_requirements TEXT DEFAULT '[]',
    setup_time INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(id)
);

-- Create equipment_capabilities table
CREATE TABLE IF NOT EXISTS equipment_capabilities (
    id TEXT PRIMARY KEY,
    equipment_id TEXT NOT NULL,
    product_id TEXT NOT NULL,
    process_id TEXT,
    capacity_per_hour INTEGER NOT NULL,
    efficiency_factor REAL DEFAULT 1.0,
    setup_time INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (equipment_id) REFERENCES equipment(id),
    FOREIGN KEY (product_id) REFERENCES products(id),
    FOREIGN KEY (process_id) REFERENCES production_processes(id)
);

-- Create operator_skills table
CREATE TABLE IF NOT EXISTS operator_skills (
    id TEXT PRIMARY KEY,
    operator_id TEXT NOT NULL,
    equipment_id TEXT NOT NULL,
    skill_level INTEGER DEFAULT 1,
    efficiency_factor REAL DEFAULT 1.0,
    certification_date TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (operator_id) REFERENCES users(id),
    FOREIGN KEY (equipment_id) REFERENCES equipment(id)
);

-- Create equipment_operators table
CREATE TABLE IF NOT EXISTS equipment_operators (
    id TEXT PRIMARY KEY,
    equipment_id TEXT NOT NULL,
    operator_id TEXT NOT NULL,
    is_primary INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (equipment_id) REFERENCES equipment(id),
    FOREIGN KEY (operator_id) REFERENCES users(id)
);

-- Create schedules table
CREATE TABLE IF NOT EXISTS schedules (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    product_id TEXT NOT NULL,
    product_name TEXT NOT NULL,
    quantity INTEGER NOT NULL,
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP NOT NULL,
    status TEXT DEFAULT 'planned',
    priority TEXT DEFAULT 'medium',
    assigned_equipment TEXT DEFAULT '[]',
    assigned_personnel TEXT DEFAULT '[]',
    required_materials TEXT DEFAULT '[]',
    progress INTEGER DEFAULT 0,
    notes TEXT DEFAULT '',
    created_by TEXT NOT NULL,
    order_id TEXT,
    delivery_date TIMESTAMP,
    predicted_completion TIMESTAMP,
    confidence_level REAL,
    optimization_score REAL,
    alternative_plans TEXT DEFAULT '[]',
    is_intelligent INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create schedule_progress table
CREATE TABLE IF NOT EXISTS schedule_progress (
    id SERIAL PRIMARY KEY,
    schedule_id TEXT NOT NULL,
    progress INTEGER NOT NULL,
    status TEXT NOT NULL,
    notes TEXT,
    updated_by TEXT NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (schedule_id) REFERENCES schedules(id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_usercode ON users(usercode);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_applications_user_id ON applications(user_id);
CREATE INDEX IF NOT EXISTS idx_applications_status ON applications(status);
CREATE INDEX IF NOT EXISTS idx_applications_created_at ON applications(created_at);
CREATE INDEX IF NOT EXISTS idx_approval_history_application_id ON approval_history(application_id);
CREATE INDEX IF NOT EXISTS idx_schedules_start_time ON schedules(start_time);
CREATE INDEX IF NOT EXISTS idx_schedules_status ON schedules(status);

-- Add comments for documentation
COMMENT ON TABLE users IS 'System users and operators';
COMMENT ON TABLE applications IS 'Application requests and approvals';
COMMENT ON TABLE schedules IS 'Production schedules and planning';
COMMENT ON TABLE equipment IS 'Manufacturing equipment information';
COMMENT ON TABLE products IS 'Product catalog and specifications';

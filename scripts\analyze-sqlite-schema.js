/**
 * SQLite数据库结构分析脚本
 * 分析现有数据库结构，生成PostgreSQL兼容的DDL
 */

const Database = require('better-sqlite3');
const fs = require('fs');
const path = require('path');

class SQLiteSchemaAnalyzer {
    constructor(dbPath) {
        this.db = new Database(dbPath);
        this.tables = [];
        this.indexes = [];
        this.foreignKeys = [];
    }

    /**
     * 分析数据库结构
     */
    analyze() {
        console.log('🔍 开始分析SQLite数据库结构...');
        
        // 获取所有表
        this.getTables();
        
        // 分析每个表的结构
        this.tables.forEach(table => {
            this.analyzeTable(table);
        });
        
        // 获取索引信息
        this.getIndexes();
        
        console.log(`✅ 分析完成！发现 ${this.tables.length} 个表`);
        
        return {
            tables: this.tables,
            indexes: this.indexes,
            foreignKeys: this.foreignKeys
        };
    }

    /**
     * 获取所有表名
     */
    getTables() {
        const query = `
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name NOT LIKE 'sqlite_%'
            ORDER BY name
        `;
        
        const rows = this.db.prepare(query).all();
        this.tables = rows.map(row => ({
            name: row.name,
            columns: [],
            constraints: []
        }));
    }

    /**
     * 分析表结构
     */
    analyzeTable(table) {
        // 获取列信息
        const pragma = this.db.prepare(`PRAGMA table_info(${table.name})`).all();
        
        table.columns = pragma.map(col => ({
            name: col.name,
            type: this.mapSQLiteTypeToPostgreSQL(col.type),
            nullable: !col.notnull,
            defaultValue: col.dflt_value,
            primaryKey: col.pk === 1
        }));

        // 获取外键信息
        const foreignKeys = this.db.prepare(`PRAGMA foreign_key_list(${table.name})`).all();
        table.foreignKeys = foreignKeys.map(fk => ({
            column: fk.from,
            referencedTable: fk.table,
            referencedColumn: fk.to,
            onDelete: fk.on_delete,
            onUpdate: fk.on_update
        }));

        console.log(`📋 表 ${table.name}: ${table.columns.length} 列, ${table.foreignKeys.length} 外键`);
    }

    /**
     * 获取索引信息
     */
    getIndexes() {
        const query = `
            SELECT name, tbl_name, sql 
            FROM sqlite_master 
            WHERE type='index' AND name NOT LIKE 'sqlite_%'
            ORDER BY tbl_name, name
        `;
        
        const rows = this.db.prepare(query).all();
        this.indexes = rows.map(row => ({
            name: row.name,
            table: row.tbl_name,
            sql: row.sql
        }));
    }

    /**
     * 映射SQLite数据类型到PostgreSQL
     */
    mapSQLiteTypeToPostgreSQL(sqliteType) {
        const typeMap = {
            'TEXT': 'TEXT',
            'INTEGER': 'INTEGER',
            'REAL': 'REAL',
            'BLOB': 'BYTEA',
            'NUMERIC': 'NUMERIC',
            'VARCHAR': 'VARCHAR',
            'CHAR': 'CHAR',
            'BOOLEAN': 'BOOLEAN',
            'DATE': 'DATE',
            'DATETIME': 'TIMESTAMP',
            'TIME': 'TIME'
        };

        const upperType = sqliteType.toUpperCase();
        
        // 处理带长度的类型
        if (upperType.includes('(')) {
            const baseType = upperType.split('(')[0];
            return typeMap[baseType] ? sqliteType.replace(baseType, typeMap[baseType]) : sqliteType;
        }
        
        return typeMap[upperType] || sqliteType;
    }

    /**
     * 生成PostgreSQL DDL
     */
    generatePostgreSQLDDL() {
        let ddl = '-- PostgreSQL数据库结构\n';
        ddl += '-- 从SQLite自动生成\n\n';
        
        // 生成表结构
        this.tables.forEach(table => {
            ddl += this.generateTableDDL(table);
            ddl += '\n';
        });
        
        // 生成索引
        this.indexes.forEach(index => {
            if (index.sql) {
                ddl += this.convertIndexToPostgreSQL(index);
                ddl += '\n';
            }
        });
        
        return ddl;
    }

    /**
     * 生成表DDL
     */
    generateTableDDL(table) {
        let ddl = `CREATE TABLE ${table.name} (\n`;
        
        // 列定义
        const columnDefs = table.columns.map(col => {
            let def = `    ${col.name} ${col.type}`;
            
            if (col.primaryKey) {
                def += ' PRIMARY KEY';
            }
            
            if (!col.nullable && !col.primaryKey) {
                def += ' NOT NULL';
            }
            
            if (col.defaultValue !== null) {
                def += ` DEFAULT ${col.defaultValue}`;
            }
            
            return def;
        });
        
        ddl += columnDefs.join(',\n');
        
        // 外键约束
        if (table.foreignKeys.length > 0) {
            ddl += ',\n';
            const fkDefs = table.foreignKeys.map(fk => {
                let fkDef = `    FOREIGN KEY (${fk.column}) REFERENCES ${fk.referencedTable}(${fk.referencedColumn})`;
                if (fk.onDelete && fk.onDelete !== 'NO ACTION') {
                    fkDef += ` ON DELETE ${fk.onDelete}`;
                }
                if (fk.onUpdate && fk.onUpdate !== 'NO ACTION') {
                    fkDef += ` ON UPDATE ${fk.onUpdate}`;
                }
                return fkDef;
            });
            ddl += fkDefs.join(',\n');
        }
        
        ddl += '\n);\n';
        
        return ddl;
    }

    /**
     * 转换索引到PostgreSQL格式
     */
    convertIndexToPostgreSQL(index) {
        if (!index.sql) return '';
        
        // 简单的SQL转换，将SQLite索引语法转换为PostgreSQL
        let sql = index.sql.replace(/CREATE INDEX/i, 'CREATE INDEX');
        sql = sql.replace(/CREATE UNIQUE INDEX/i, 'CREATE UNIQUE INDEX');
        
        return sql + ';';
    }

    close() {
        this.db.close();
    }
}

// 执行分析
async function main() {
    const dbPath = path.join(__dirname, '../backend/database/application_system.db');
    
    if (!fs.existsSync(dbPath)) {
        console.error('❌ SQLite数据库文件不存在:', dbPath);
        process.exit(1);
    }
    
    const analyzer = new SQLiteSchemaAnalyzer(dbPath);
    
    try {
        const schema = analyzer.analyze();
        
        // 生成PostgreSQL DDL
        const ddl = analyzer.generatePostgreSQLDDL();
        
        // 保存DDL文件
        const outputPath = path.join(__dirname, 'postgresql-schema.sql');
        fs.writeFileSync(outputPath, ddl);
        
        console.log(`📄 PostgreSQL DDL已生成: ${outputPath}`);
        
        // 生成分析报告
        const report = {
            timestamp: new Date().toISOString(),
            totalTables: schema.tables.length,
            totalIndexes: schema.indexes.length,
            tables: schema.tables.map(t => ({
                name: t.name,
                columns: t.columns.length,
                foreignKeys: t.foreignKeys.length
            }))
        };
        
        const reportPath = path.join(__dirname, 'schema-analysis-report.json');
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        
        console.log(`📊 分析报告已生成: ${reportPath}`);
        
    } catch (error) {
        console.error('❌ 分析失败:', error);
    } finally {
        analyzer.close();
    }
}

if (require.main === module) {
    main();
}

module.exports = SQLiteSchemaAnalyzer;

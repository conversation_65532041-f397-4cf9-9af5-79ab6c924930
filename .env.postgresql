# PostgreSQL Configuration
# Copy this file to .env and update with your actual values

# PostgreSQL Database Configuration
PG_USER=postgres
PG_HOST=localhost
PG_DATABASE=makrite_managementsystem
PG_PASSWORD=your_postgres_password_here
PG_PORT=5432

# Connection Pool Settings
PG_MAX_CONNECTIONS=20
PG_MIN_CONNECTIONS=2
PG_IDLE_TIMEOUT=30000
PG_CONNECTION_TIMEOUT=2000

# Environment
NODE_ENV=development

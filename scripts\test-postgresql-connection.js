/**
 * PostgreSQL连接测试脚本
 * 验证数据库连接和基本操作
 */

require('dotenv').config({ path: '.env.postgresql' });
const { Pool } = require('pg');

async function testConnection() {
    console.log('🔍 开始测试PostgreSQL连接...');
    
    // 获取密码
    const password = process.env.PG_PASSWORD || process.argv[2] || '';

    if (!password) {
        console.log('❌ 请提供PostgreSQL密码:');
        console.log('   方式1: set PG_PASSWORD=your_password && node scripts/test-postgresql-connection.js');
        console.log('   方式2: node scripts/test-postgresql-connection.js your_password');
        process.exit(1);
    }

    const pool = new Pool({
        user: 'postgres',
        host: 'localhost',
        database: 'makrite_managementsystem',
        password: password,
        port: 5432,
        max: 5,
        idleTimeoutMillis: 30000,
        connectionTimeoutMillis: 2000,
    });

    try {
        // 测试基本连接
        console.log('📡 测试数据库连接...');
        const client = await pool.connect();
        console.log('✅ 数据库连接成功！');
        
        // 测试查询
        console.log('📋 测试基本查询...');
        const result = await client.query('SELECT NOW() as current_time, version() as version');
        console.log('✅ 查询成功！');
        console.log(`⏰ 当前时间: ${result.rows[0].current_time}`);
        console.log(`🗄️ 数据库版本: ${result.rows[0].version.split(',')[0]}`);
        
        // 测试表结构
        console.log('📊 检查表结构...');
        const tablesResult = await client.query(`
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            ORDER BY table_name
        `);
        
        console.log(`✅ 发现 ${tablesResult.rows.length} 个表:`);
        tablesResult.rows.forEach((row, index) => {
            console.log(`   ${index + 1}. ${row.table_name}`);
        });
        
        // 测试插入和查询
        console.log('🧪 测试数据操作...');
        
        // 插入测试用户
        const insertResult = await client.query(`
            INSERT INTO users (id, usercode, username, password, role, department, email)
            VALUES ($1, $2, $3, $4, $5, $6, $7)
            ON CONFLICT (id) DO NOTHING
            RETURNING id
        `, [
            'test-user-001',
            'TEST001',
            'Test User',
            'test123',
            'user',
            'Test Department',
            '<EMAIL>'
        ]);
        
        if (insertResult.rows.length > 0) {
            console.log('✅ 测试用户插入成功！');
        } else {
            console.log('ℹ️ 测试用户已存在，跳过插入');
        }
        
        // 查询测试用户
        const selectResult = await client.query(`
            SELECT id, usercode, username, role, department, created_at
            FROM users 
            WHERE usercode = $1
        `, ['TEST001']);
        
        if (selectResult.rows.length > 0) {
            console.log('✅ 测试用户查询成功！');
            console.log('📄 用户信息:', selectResult.rows[0]);
        }
        
        // 测试事务
        console.log('🔄 测试事务操作...');
        await client.query('BEGIN');
        
        const transactionResult = await client.query(`
            INSERT INTO departments (id, name, description)
            VALUES ($1, $2, $3)
            ON CONFLICT (name) DO NOTHING
            RETURNING id
        `, [
            'test-dept-001',
            'Test Department',
            'This is a test department'
        ]);
        
        await client.query('COMMIT');
        console.log('✅ 事务操作成功！');
        
        // 测试连接池统计
        console.log('📈 连接池统计:');
        console.log(`   总连接数: ${pool.totalCount}`);
        console.log(`   空闲连接数: ${pool.idleCount}`);
        console.log(`   等待连接数: ${pool.waitingCount}`);
        
        client.release();
        
        console.log('\n🎉 所有测试通过！PostgreSQL数据库配置正确。');
        
        return true;
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        console.error('🔧 请检查以下配置:');
        console.error('   1. PostgreSQL服务是否启动');
        console.error('   2. 数据库名称是否正确: makrite_managementsystem');
        console.error('   3. 用户名和密码是否正确');
        console.error('   4. 端口号是否正确: 5432');
        
        return false;
        
    } finally {
        await pool.end();
    }
}

// 执行测试
if (require.main === module) {
    testConnection()
        .then(success => {
            process.exit(success ? 0 : 1);
        })
        .catch(error => {
            console.error('❌ 测试执行失败:', error);
            process.exit(1);
        });
}

module.exports = testConnection;

/**
 * PostgreSQL连接测试脚本
 * 用于验证PostgreSQL安装和连接配置
 */

const { Pool, Client } = require('pg');

// 数据库连接配置
const config = {
    user: 'postgres',           // 用户名
    host: 'localhost',          // 主机地址
    database: 'postgres',       // 默认数据库
    password: 'your_actual_password',  // 请替换为您安装时设置的实际密码
    port: 5432,                 // 端口号
};

// 测试1：基础连接测试
async function testBasicConnection() {
    console.log('🔍 测试1：基础连接测试...');
    
    const client = new Client(config);
    
    try {
        await client.connect();
        console.log('✅ PostgreSQL连接成功！');
        
        // 查询版本信息
        const result = await client.query('SELECT version()');
        console.log('📋 PostgreSQL版本:', result.rows[0].version);
        
        await client.end();
        return true;
    } catch (error) {
        console.error('❌ 连接失败:', error.message);
        return false;
    }
}

// 测试2：连接池测试
async function testConnectionPool() {
    console.log('\n🔍 测试2：连接池测试...');
    
    const pool = new Pool({
        ...config,
        max: 10,                    // 最大连接数
        idleTimeoutMillis: 30000,   // 空闲超时
        connectionTimeoutMillis: 2000, // 连接超时
    });
    
    try {
        const client = await pool.connect();
        console.log('✅ 连接池连接成功！');
        
        // 测试查询
        const result = await client.query('SELECT NOW() as current_time');
        console.log('⏰ 当前时间:', result.rows[0].current_time);
        
        client.release();
        await pool.end();
        return true;
    } catch (error) {
        console.error('❌ 连接池测试失败:', error.message);
        return false;
    }
}

// 测试3：创建项目数据库
async function testCreateDatabase() {
    console.log('\n🔍 测试3：创建项目数据库...');
    
    const client = new Client(config);
    
    try {
        await client.connect();
        
        // 检查数据库是否已存在
        const checkDb = await client.query(
            "SELECT 1 FROM pg_database WHERE datname = 'makrite_system'"
        );
        
        if (checkDb.rows.length === 0) {
            // 创建数据库
            await client.query('CREATE DATABASE makrite_system');
            console.log('✅ 项目数据库 makrite_system 创建成功！');
        } else {
            console.log('ℹ️  项目数据库 makrite_system 已存在');
        }
        
        await client.end();
        return true;
    } catch (error) {
        console.error('❌ 创建数据库失败:', error.message);
        return false;
    }
}

// 测试4：项目数据库操作测试
async function testProjectDatabase() {
    console.log('\n🔍 测试4：项目数据库操作测试...');
    
    const projectConfig = {
        ...config,
        database: 'makrite_system'
    };
    
    const client = new Client(projectConfig);
    
    try {
        await client.connect();
        console.log('✅ 连接到项目数据库成功！');
        
        // 创建测试表
        await client.query(`
            CREATE TABLE IF NOT EXISTS connection_test (
                id SERIAL PRIMARY KEY,
                test_name VARCHAR(100),
                test_time TIMESTAMP DEFAULT NOW(),
                test_data JSONB
            )
        `);
        console.log('✅ 测试表创建成功！');
        
        // 插入测试数据
        const insertResult = await client.query(`
            INSERT INTO connection_test (test_name, test_data) 
            VALUES ($1, $2) 
            RETURNING *
        `, ['PostgreSQL连接测试', { status: 'success', version: '1.0' }]);
        
        console.log('✅ 测试数据插入成功:', insertResult.rows[0]);
        
        // 查询测试数据
        const selectResult = await client.query('SELECT * FROM connection_test ORDER BY id DESC LIMIT 1');
        console.log('✅ 数据查询成功:', selectResult.rows[0]);
        
        // 清理测试数据
        await client.query('DROP TABLE connection_test');
        console.log('✅ 测试表清理完成！');
        
        await client.end();
        return true;
    } catch (error) {
        console.error('❌ 项目数据库测试失败:', error.message);
        return false;
    }
}

// 测试5：性能基准测试
async function testPerformance() {
    console.log('\n🔍 测试5：性能基准测试...');
    
    const pool = new Pool({
        ...config,
        database: 'makrite_system',
        max: 10
    });
    
    try {
        const startTime = Date.now();
        
        // 并发查询测试
        const promises = [];
        for (let i = 0; i < 10; i++) {
            promises.push(pool.query('SELECT $1 as test_number', [i]));
        }
        
        const results = await Promise.all(promises);
        const endTime = Date.now();
        
        console.log(`✅ 10个并发查询完成，耗时: ${endTime - startTime}ms`);
        console.log('📊 查询结果数量:', results.length);
        
        await pool.end();
        return true;
    } catch (error) {
        console.error('❌ 性能测试失败:', error.message);
        return false;
    }
}

// 主测试函数
async function runAllTests() {
    console.log('🚀 开始PostgreSQL连接测试...\n');
    console.log('⚠️  请确保已修改配置中的密码！\n');
    
    const tests = [
        { name: '基础连接测试', fn: testBasicConnection },
        { name: '连接池测试', fn: testConnectionPool },
        { name: '创建项目数据库', fn: testCreateDatabase },
        { name: '项目数据库操作测试', fn: testProjectDatabase },
        { name: '性能基准测试', fn: testPerformance }
    ];
    
    let passedTests = 0;
    
    for (const test of tests) {
        try {
            const result = await test.fn();
            if (result) {
                passedTests++;
            }
        } catch (error) {
            console.error(`❌ ${test.name} 执行异常:`, error.message);
        }
    }
    
    console.log('\n📊 测试结果汇总:');
    console.log(`✅ 通过测试: ${passedTests}/${tests.length}`);
    console.log(`❌ 失败测试: ${tests.length - passedTests}/${tests.length}`);
    
    if (passedTests === tests.length) {
        console.log('\n🎉 所有测试通过！PostgreSQL配置正确，可以开始迁移！');
    } else {
        console.log('\n⚠️  部分测试失败，请检查配置和错误信息');
    }
}

// 运行测试
if (require.main === module) {
    runAllTests().catch(console.error);
}

module.exports = {
    testBasicConnection,
    testConnectionPool,
    testCreateDatabase,
    testProjectDatabase,
    testPerformance
};

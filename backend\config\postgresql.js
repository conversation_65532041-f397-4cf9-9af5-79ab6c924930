/**
 * PostgreSQL数据库配置
 * 支持连接池和性能优化
 */

const { Pool } = require('pg');
const logger = require('../utils/logger');

class PostgreSQLManager {
    constructor() {
        this.pool = null;
        this.isConnected = false;
        this.init();
    }

    /**
     * 初始化PostgreSQL连接池
     */
    init() {
        try {
            this.pool = new Pool({
                user: process.env.PG_USER || 'postgres',
                host: process.env.PG_HOST || 'localhost',
                database: process.env.PG_DATABASE || 'makrite_managementsystem',
                password: process.env.PG_PASSWORD || '',
                port: process.env.PG_PORT || 5432,
                
                // 连接池配置
                max: 20,                    // 最大连接数
                min: 2,                     // 最小连接数
                idleTimeoutMillis: 30000,   // 空闲连接超时
                connectionTimeoutMillis: 2000, // 连接超时
                
                // 性能优化配置
                statement_timeout: 30000,   // 语句超时
                query_timeout: 30000,       // 查询超时
                
                // SSL配置（生产环境建议启用）
                ssl: process.env.NODE_ENV === 'production' ? {
                    rejectUnauthorized: false
                } : false
            });

            // 连接池事件监听
            this.pool.on('connect', (client) => {
                if (process.env.VERBOSE_LOGS === 'true') {
                    logger.info('PostgreSQL客户端连接成功');
                }
            });

            this.pool.on('error', (err, client) => {
                logger.error('PostgreSQL连接池错误:', err);
            });

            this.pool.on('remove', (client) => {
                if (process.env.VERBOSE_LOGS === 'true') {
                    logger.info('PostgreSQL客户端连接移除');
                }
            });

            this.isConnected = true;
            
            if (process.env.VERBOSE_LOGS === 'true') {
                logger.info('PostgreSQL连接池初始化成功');
            }

        } catch (error) {
            logger.error('PostgreSQL初始化失败:', error);
            throw error;
        }
    }

    /**
     * 执行查询
     */
    async query(text, params = []) {
        const start = Date.now();
        
        try {
            const result = await this.pool.query(text, params);
            const duration = Date.now() - start;
            
            // 记录慢查询
            if (duration > 1000) {
                logger.warn('慢查询检测', {
                    query: text.substring(0, 100),
                    duration: `${duration}ms`,
                    params: params.length
                });
            }
            
            return result;
        } catch (error) {
            logger.error('PostgreSQL查询错误:', {
                error: error.message,
                query: text.substring(0, 100),
                params: params.length
            });
            throw error;
        }
    }

    /**
     * 执行事务
     */
    async transaction(callback) {
        const client = await this.pool.connect();
        
        try {
            await client.query('BEGIN');
            const result = await callback(client);
            await client.query('COMMIT');
            return result;
        } catch (error) {
            await client.query('ROLLBACK');
            throw error;
        } finally {
            client.release();
        }
    }

    /**
     * 健康检查
     */
    async healthCheck() {
        try {
            const result = await this.query('SELECT NOW() as current_time, version() as version');
            return {
                status: 'healthy',
                timestamp: result.rows[0].current_time,
                version: result.rows[0].version,
                poolStats: {
                    totalCount: this.pool.totalCount,
                    idleCount: this.pool.idleCount,
                    waitingCount: this.pool.waitingCount
                }
            };
        } catch (error) {
            return {
                status: 'unhealthy',
                error: error.message
            };
        }
    }

    /**
     * 获取连接池统计信息
     */
    getPoolStats() {
        return {
            totalCount: this.pool.totalCount,
            idleCount: this.pool.idleCount,
            waitingCount: this.pool.waitingCount
        };
    }

    /**
     * 关闭连接池
     */
    async close() {
        if (this.pool) {
            await this.pool.end();
            this.isConnected = false;
            logger.info('PostgreSQL连接池已关闭');
        }
    }
}

// 创建单例实例
const postgresManager = new PostgreSQLManager();

module.exports = {
    PostgreSQLManager,
    postgresManager,
    query: (text, params) => postgresManager.query(text, params),
    transaction: (callback) => postgresManager.transaction(callback),
    healthCheck: () => postgresManager.healthCheck(),
    getPoolStats: () => postgresManager.getPoolStats()
};

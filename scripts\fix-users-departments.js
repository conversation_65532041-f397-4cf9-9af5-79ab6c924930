/**
 * 修复users和departments表的数据迁移
 * 专门处理布尔值字段问题
 */

require('dotenv').config({ path: '.env.postgresql' });
const Database = require('better-sqlite3');
const { Pool } = require('pg');
const path = require('path');

async function fixUsersDepartments() {
    console.log('🔧 修复users和departments表数据...');
    
    // 连接数据库
    const sqlitePath = path.join(__dirname, '../backend/database/application_system.db');
    const sqliteDb = new Database(sqlitePath, { readonly: true });
    
    const pgPool = new Pool({
        user: process.env.PG_USER || 'postgres',
        host: process.env.PG_HOST || 'localhost',
        database: process.env.PG_DATABASE || 'makrite_managementsystem',
        password: process.env.PG_PASSWORD,
        port: process.env.PG_PORT || 5432,
    });

    try {
        // 修复users表
        console.log('👥 修复users表...');
        
        // 清空users表
        await pgPool.query('TRUNCATE TABLE users RESTART IDENTITY CASCADE');
        
        // 获取SQLite users数据
        const users = sqliteDb.prepare('SELECT * FROM users').all();
        console.log(`发现 ${users.length} 个用户`);
        
        for (const user of users) {
            try {
                await pgPool.query(`
                    INSERT INTO users (
                        id, usercode, username, password, role, department, email,
                        active, permissions, has_signature, signature_path, signature_base64,
                        last_login_at, last_active_at, created_at, updated_at
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16)
                `, [
                    user.id,
                    user.usercode,
                    user.username,
                    user.password,
                    user.role,
                    user.department,
                    user.email,
                    user.active || 1,  // 保持为integer
                    user.permissions || '[]',
                    user.has_signature || 0,  // 保持为integer
                    user.signature_path,
                    user.signature_base64,
                    user.last_login_at ? new Date(user.last_login_at).toISOString() : null,
                    user.last_active_at ? new Date(user.last_active_at).toISOString() : null,
                    user.created_at ? new Date(user.created_at).toISOString() : new Date().toISOString(),
                    user.updated_at ? new Date(user.updated_at).toISOString() : new Date().toISOString()
                ]);
                console.log(`   ✅ 用户: ${user.username}`);
            } catch (error) {
                console.error(`   ❌ 用户 ${user.username} 插入失败:`, error.message);
            }
        }
        
        // 修复departments表
        console.log('\n🏢 修复departments表...');
        
        // 清空departments表
        await pgPool.query('TRUNCATE TABLE departments RESTART IDENTITY CASCADE');
        
        // 获取SQLite departments数据
        const departments = sqliteDb.prepare('SELECT * FROM departments').all();
        console.log(`发现 ${departments.length} 个部门`);
        
        for (const dept of departments) {
            try {
                await pgPool.query(`
                    INSERT INTO departments (
                        id, name, description, active, created_at, updated_at
                    ) VALUES ($1, $2, $3, $4, $5, $6)
                `, [
                    dept.id,
                    dept.name,
                    dept.description,
                    dept.active || 1,  // 保持为integer
                    dept.created_at ? new Date(dept.created_at).toISOString() : new Date().toISOString(),
                    dept.updated_at ? new Date(dept.updated_at).toISOString() : new Date().toISOString()
                ]);
                console.log(`   ✅ 部门: ${dept.name}`);
            } catch (error) {
                console.error(`   ❌ 部门 ${dept.name} 插入失败:`, error.message);
            }
        }
        
        // 修复permission_templates表
        console.log('\n🔐 修复permission_templates表...');
        
        // 清空permission_templates表
        await pgPool.query('TRUNCATE TABLE permission_templates RESTART IDENTITY CASCADE');
        
        // 获取SQLite permission_templates数据
        const templates = sqliteDb.prepare('SELECT * FROM permission_templates').all();
        console.log(`发现 ${templates.length} 个权限模板`);
        
        for (const template of templates) {
            try {
                await pgPool.query(`
                    INSERT INTO permission_templates (
                        id, name, description, permissions, is_built_in, created_at, updated_at
                    ) VALUES ($1, $2, $3, $4, $5, $6, $7)
                `, [
                    template.id,
                    template.name,
                    template.description,
                    template.permissions || '[]',
                    template.is_built_in || 0,  // 保持为integer
                    template.created_at ? new Date(template.created_at).toISOString() : new Date().toISOString(),
                    template.updated_at ? new Date(template.updated_at).toISOString() : new Date().toISOString()
                ]);
                console.log(`   ✅ 权限模板: ${template.name}`);
            } catch (error) {
                console.error(`   ❌ 权限模板 ${template.name} 插入失败:`, error.message);
            }
        }
        
        // 验证结果
        console.log('\n🔍 验证修复结果...');
        
        const userCount = await pgPool.query('SELECT COUNT(*) as count FROM users');
        const deptCount = await pgPool.query('SELECT COUNT(*) as count FROM departments');
        const templateCount = await pgPool.query('SELECT COUNT(*) as count FROM permission_templates');
        
        console.log(`✅ users表: ${userCount.rows[0].count} 条记录`);
        console.log(`✅ departments表: ${deptCount.rows[0].count} 条记录`);
        console.log(`✅ permission_templates表: ${templateCount.rows[0].count} 条记录`);
        
        console.log('\n🎉 修复完成！');
        
    } catch (error) {
        console.error('❌ 修复失败:', error);
    } finally {
        sqliteDb.close();
        await pgPool.end();
    }
}

// 执行修复
if (require.main === module) {
    fixUsersDepartments()
        .then(() => process.exit(0))
        .catch(error => {
            console.error('❌ 执行失败:', error);
            process.exit(1);
        });
}

module.exports = fixUsersDepartments;

-- PostgreSQL数据库结构
-- 从SQLite自动生成

CREATE TABLE application_attachments (
    id TEXT PRIMARY KEY,
    application_id TEXT NOT NULL,
    name TEXT NOT NULL,
    path TEXT NOT NULL,
    filename TEXT,
    type TEXT,
    size INTEGER,
    uploaded_at TEXT NOT NULL,
    FOREIGN KEY (application_id) REFERENCES applications(id) ON DELETE CASCADE
);

CREATE TABLE application_drafts (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    title TEXT,
    type TEXT DEFAULT 'standard',
    draft_data TEXT NOT NULL,
    auto_saved INTEGER DEFAULT 0,
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

CREATE TABLE applications (
    id TEXT PRIMARY KEY,
    application_number TEXT NOT NULL,
    user_id TEXT NOT NULL,
    applicant TEXT NOT NULL,
    department TEXT,
    date TEXT NOT NULL,
    content TEXT NOT NULL,
    amount TEXT,
    priority TEXT DEFAULT 'normal',
    type TEXT DEFAULT 'standard',
    status TEXT DEFAULT 'pending',
    current_stage TEXT,
    need_manager_approval INTEGER DEFAULT 0,
    selected_factory_managers TEXT DEFAULT '[]',
    pdf_path TEXT,
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL,
    selected_managers TEXT DEFAULT '[]',
    need_ceo_approval INTEGER DEFAULT 1,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

CREATE TABLE approval_history (
    id INTEGER PRIMARY KEY,
    application_id TEXT NOT NULL,
    stage TEXT NOT NULL,
    approver_id TEXT NOT NULL,
    approver_name TEXT NOT NULL,
    approver_role TEXT NOT NULL,
    action TEXT NOT NULL,
    comment TEXT,
    signature_path TEXT,
    timestamp TEXT NOT NULL,
    FOREIGN KEY (approver_id) REFERENCES users(id),
    FOREIGN KEY (application_id) REFERENCES applications(id) ON DELETE CASCADE
);

CREATE TABLE customer_file_attachments (
    id TEXT PRIMARY KEY,
    file_id TEXT NOT NULL,
    original_filename TEXT NOT NULL,
    stored_filename TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_size INTEGER NOT NULL,
    file_type TEXT NOT NULL,
    mime_type TEXT NOT NULL,
    uploaded_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (file_id) REFERENCES customer_files(id) ON DELETE CASCADE
);

CREATE TABLE customer_file_confirmations (
    id TEXT PRIMARY KEY,
    file_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    confirmed_at TEXT,
    downloaded_at TEXT,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (file_id) REFERENCES customer_files(id) ON DELETE CASCADE
);

CREATE TABLE customer_file_notifications (
    id TEXT PRIMARY KEY,
    file_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    notified_at TEXT NOT NULL,
    email_sent INTEGER DEFAULT 0,
    confirmed INTEGER DEFAULT 0,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (file_id) REFERENCES customer_files(id) ON DELETE CASCADE
);

CREATE TABLE customer_file_number_reservations (
    file_number TEXT PRIMARY KEY,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE customer_files (
    id TEXT PRIMARY KEY,
    file_number TEXT NOT NULL,
    customer_name TEXT NOT NULL,
    model TEXT NOT NULL,
    batch TEXT,
    version TEXT NOT NULL,
    content_type TEXT NOT NULL,
    initial_content TEXT,
    change_content TEXT,
    uploaded_by TEXT NOT NULL,
    uploaded_at TEXT NOT NULL,
    status TEXT DEFAULT 'pending',
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (uploaded_by) REFERENCES users(id)
);

CREATE TABLE departments (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    active INTEGER DEFAULT 1,
    sort_order INTEGER DEFAULT 0,
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL
);

CREATE TABLE equipment (
    id TEXT PRIMARY KEY,
    code TEXT NOT NULL,
    name TEXT NOT NULL,
    area TEXT NOT NULL,
    location TEXT NOT NULL,
    responsible TEXT NOT NULL,
    manufacture_date TEXT NOT NULL,
    status TEXT DEFAULT 'idle',
    specifications TEXT DEFAULT '{}',
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE equipment_capabilities (
    id TEXT PRIMARY KEY,
    equipment_id TEXT NOT NULL,
    product_id TEXT NOT NULL,
    process_id TEXT,
    capacity_per_hour INTEGER NOT NULL,
    efficiency_factor REAL DEFAULT 1.0,
    setup_time INTEGER DEFAULT 0,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (process_id) REFERENCES production_processes(id),
    FOREIGN KEY (product_id) REFERENCES products(id),
    FOREIGN KEY (equipment_id) REFERENCES equipment(id)
);

CREATE TABLE equipment_capacity (
    id TEXT PRIMARY KEY,
    equipment_id TEXT NOT NULL,
    product_id TEXT NOT NULL,
    operator_id TEXT,
    average_capacity REAL DEFAULT 0,
    max_capacity REAL DEFAULT 0,
    efficiency_rate REAL DEFAULT 1.0,
    setup_time REAL DEFAULT 0,
    changeover_time REAL DEFAULT 0,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (operator_id) REFERENCES users(id),
    FOREIGN KEY (product_id) REFERENCES products(id),
    FOREIGN KEY (equipment_id) REFERENCES equipment(id)
);

CREATE TABLE equipment_health (
    id TEXT PRIMARY KEY,
    equipment_id TEXT NOT NULL,
    total_score INTEGER NOT NULL,
    health_level TEXT NOT NULL,
    age_score INTEGER NOT NULL,
    repair_frequency_score INTEGER NOT NULL,
    fault_severity_score INTEGER NOT NULL,
    maintenance_score INTEGER NOT NULL,
    assessment_date TEXT NOT NULL,
    assessor TEXT NOT NULL,
    calculation_details TEXT DEFAULT '{}',
    recommendations TEXT DEFAULT '[]',
    next_maintenance_date TEXT,
    failure_probability REAL DEFAULT 0,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (equipment_id) REFERENCES equipment(id)
);

CREATE TABLE equipment_health_history (
    id TEXT PRIMARY KEY,
    equipment_id TEXT NOT NULL,
    total_score INTEGER NOT NULL,
    health_level TEXT NOT NULL,
    age_score INTEGER NOT NULL,
    repair_frequency_score INTEGER NOT NULL,
    fault_severity_score INTEGER NOT NULL,
    maintenance_score INTEGER NOT NULL,
    assessment_date TEXT NOT NULL,
    assessor TEXT NOT NULL,
    calculation_details TEXT DEFAULT '{}',
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (equipment_id) REFERENCES equipment(id)
);

CREATE TABLE equipment_maintenance (
    id TEXT PRIMARY KEY,
    equipment_id TEXT NOT NULL,
    type TEXT NOT NULL,
    severity_level TEXT DEFAULT '',
    description TEXT NOT NULL,
    maintenance_date TEXT NOT NULL,
    start_time TEXT DEFAULT '',
    end_time TEXT DEFAULT '',
    cost REAL DEFAULT 0,
    technician TEXT NOT NULL,
    status TEXT DEFAULT 'pending',
    notes TEXT DEFAULT '',
    result TEXT DEFAULT '',
    reviewer TEXT DEFAULT '',
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (equipment_id) REFERENCES equipment(id)
);

CREATE TABLE equipment_operators (
    id TEXT PRIMARY KEY,
    equipment_id TEXT NOT NULL,
    operator_id TEXT NOT NULL,
    is_primary INTEGER DEFAULT 0,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (operator_id) REFERENCES users(id),
    FOREIGN KEY (equipment_id) REFERENCES equipment(id)
);

CREATE TABLE factories (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT DEFAULT '',
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE file_management_attachments (
    id TEXT PRIMARY KEY,
    file_record_id TEXT NOT NULL,
    original_filename TEXT NOT NULL,
    stored_filename TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_size INTEGER NOT NULL,
    file_type TEXT NOT NULL,
    mime_type TEXT NOT NULL,
    uploaded_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (file_record_id) REFERENCES file_management_files(id) ON DELETE CASCADE
);

CREATE TABLE file_management_customers (
    id TEXT PRIMARY KEY,
    customer_name TEXT NOT NULL,
    customer_code TEXT,
    contact_person TEXT,
    contact_email TEXT,
    contact_phone TEXT,
    address TEXT,
    description TEXT DEFAULT '',
    active INTEGER DEFAULT 1,
    created_by TEXT NOT NULL,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_by TEXT,
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (updated_by) REFERENCES users(id)
);

CREATE TABLE file_management_files (
    id TEXT PRIMARY KEY,
    file_number TEXT NOT NULL,
    customer_id TEXT NOT NULL,
    product_id TEXT NOT NULL,
    title TEXT NOT NULL,
    description TEXT DEFAULT '',
    version INTEGER DEFAULT 1,
    is_first_version INTEGER DEFAULT 1,
    change_description TEXT DEFAULT '',
    status TEXT DEFAULT 'active',
    uploaded_by TEXT NOT NULL,
    uploaded_at TEXT NOT NULL,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (uploaded_by) REFERENCES users(id),
    FOREIGN KEY (product_id) REFERENCES file_management_products(id) ON DELETE CASCADE,
    FOREIGN KEY (customer_id) REFERENCES file_management_customers(id) ON DELETE CASCADE
);

CREATE TABLE file_management_notifications (
    id TEXT PRIMARY KEY,
    file_record_id TEXT NOT NULL,
    notified_user_id TEXT NOT NULL,
    notification_type TEXT DEFAULT 'new_file',
    sent_at TEXT DEFAULT CURRENT_TIMESTAMP,
    confirmed_at TEXT,
    confirmed INTEGER DEFAULT 0,
    FOREIGN KEY (notified_user_id) REFERENCES users(id),
    FOREIGN KEY (file_record_id) REFERENCES file_management_files(id) ON DELETE CASCADE
);

CREATE TABLE file_management_number_reservations (
    file_number TEXT PRIMARY KEY,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE file_management_products (
    id TEXT PRIMARY KEY,
    customer_id TEXT NOT NULL,
    product_model TEXT NOT NULL,
    product_name TEXT,
    batch_number TEXT,
    specification TEXT DEFAULT '',
    description TEXT DEFAULT '',
    active INTEGER DEFAULT 1,
    created_by TEXT NOT NULL,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (customer_id) REFERENCES file_management_customers(id) ON DELETE CASCADE
);

CREATE TABLE inventory_item_tags (
    id INTEGER PRIMARY KEY,
    item_id TEXT NOT NULL,
    tag_id TEXT NOT NULL,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (tag_id) REFERENCES inventory_tags(id) ON DELETE CASCADE,
    FOREIGN KEY (item_id) REFERENCES inventory_items(id) ON DELETE CASCADE
);

CREATE TABLE inventory_items (
    id TEXT PRIMARY KEY,
    item_code TEXT NOT NULL,
    name TEXT NOT NULL,
    description TEXT DEFAULT '',
    category TEXT DEFAULT '',
    unit TEXT NOT NULL,
    min_stock INTEGER DEFAULT 0,
    max_stock INTEGER DEFAULT 0,
    current_stock INTEGER DEFAULT 0,
    location TEXT DEFAULT '',
    supplier TEXT DEFAULT '',
    price REAL DEFAULT 0,
    status TEXT DEFAULT 'active',
    created_by TEXT NOT NULL,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

CREATE TABLE inventory_label_templates (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT DEFAULT '',
    template_data TEXT NOT NULL,
    is_default INTEGER DEFAULT 0,
    created_by TEXT NOT NULL,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

CREATE TABLE inventory_operations (
    id TEXT PRIMARY KEY,
    item_id TEXT NOT NULL,
    operation_type TEXT NOT NULL,
    quantity INTEGER NOT NULL,
    reason TEXT DEFAULT '',
    operator TEXT NOT NULL,
    operation_date TEXT NOT NULL,
    before_stock INTEGER NOT NULL,
    after_stock INTEGER NOT NULL,
    notes TEXT DEFAULT '',
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (operator) REFERENCES users(id),
    FOREIGN KEY (item_id) REFERENCES inventory_items(id)
);

CREATE TABLE inventory_records (
    id TEXT PRIMARY KEY,
    item_id TEXT NOT NULL,
    type TEXT NOT NULL,
    quantity INTEGER NOT NULL,
    unit_price REAL DEFAULT 0,
    total_amount REAL DEFAULT 0,
    before_stock INTEGER DEFAULT 0,
    after_stock INTEGER DEFAULT 0,
    reason TEXT DEFAULT '',
    operator TEXT NOT NULL,
    reference_no TEXT DEFAULT '',
    notes TEXT DEFAULT '',
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (item_id) REFERENCES inventory_items(id)
);

CREATE TABLE inventory_tags (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT DEFAULT '',
    color TEXT DEFAULT '#3B82F6',
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE ip_whitelist (
    id INTEGER PRIMARY KEY,
    ip_address TEXT NOT NULL,
    ip_type TEXT NOT NULL DEFAULT 'single',
    ip_start TEXT,
    ip_end TEXT,
    description TEXT,
    group_name TEXT DEFAULT 'default',
    is_active INTEGER NOT NULL DEFAULT 1,
    expires_at TEXT,
    created_by TEXT NOT NULL,
    created_at TEXT NOT NULL DEFAULT datetime('now', 'localtime'),
    updated_at TEXT NOT NULL DEFAULT datetime('now', 'localtime')
);

CREATE TABLE migrations (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    executed_at TEXT NOT NULL,
    success INTEGER NOT NULL DEFAULT 1
);

CREATE TABLE operator_skills (
    id TEXT PRIMARY KEY,
    operator_id TEXT NOT NULL,
    equipment_id TEXT NOT NULL,
    skill_level INTEGER DEFAULT 1,
    certification_date TEXT,
    experience_years REAL DEFAULT 0,
    average_efficiency REAL DEFAULT 1.0,
    max_efficiency REAL DEFAULT 1.0,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (equipment_id) REFERENCES equipment(id),
    FOREIGN KEY (operator_id) REFERENCES users(id)
);

CREATE TABLE permission_templates (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    permissions TEXT NOT NULL DEFAULT '[]',
    is_built_in INTEGER DEFAULT 0,
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL
);

CREATE TABLE production_processes (
    id TEXT PRIMARY KEY,
    product_id TEXT NOT NULL,
    process_name TEXT NOT NULL,
    sequence_order INTEGER NOT NULL,
    standard_time REAL DEFAULT 0,
    required_equipment_type TEXT DEFAULT '',
    quality_requirements TEXT DEFAULT '{}',
    dependencies TEXT DEFAULT '[]',
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES products(id)
);

CREATE TABLE products (
    id TEXT PRIMARY KEY,
    code TEXT NOT NULL,
    name TEXT NOT NULL,
    category TEXT DEFAULT '',
    specifications TEXT DEFAULT '{}',
    standard_work_hours REAL DEFAULT 0,
    complexity_level INTEGER DEFAULT 1,
    required_processes TEXT DEFAULT '[]',
    quality_requirements TEXT DEFAULT '{}',
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE quality_report_files (
    id TEXT PRIMARY KEY,
    report_id TEXT NOT NULL,
    original_filename TEXT NOT NULL,
    stored_filename TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_size INTEGER NOT NULL,
    file_type TEXT NOT NULL,
    mime_type TEXT NOT NULL,
    uploaded_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (report_id) REFERENCES quality_reports(id) ON DELETE CASCADE
);

CREATE TABLE quality_report_number_reservations (
    report_number TEXT PRIMARY KEY,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE quality_report_numbers (
    report_number TEXT PRIMARY KEY,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE quality_reports (
    id TEXT PRIMARY KEY,
    report_number TEXT NOT NULL,
    title TEXT NOT NULL,
    description TEXT DEFAULT '',
    test_type TEXT NOT NULL,
    test_date TEXT NOT NULL,
    sample_info TEXT DEFAULT '',
    test_method TEXT DEFAULT '',
    test_standard TEXT DEFAULT '',
    test_result TEXT DEFAULT '',
    conclusion TEXT DEFAULT '',
    uploaded_by TEXT NOT NULL,
    uploaded_at TEXT NOT NULL,
    status TEXT DEFAULT 'published',
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (uploaded_by) REFERENCES users(id)
);

CREATE TABLE resources (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    type TEXT NOT NULL,
    status TEXT DEFAULT 'available',
    capacity INTEGER DEFAULT 0,
    current_load INTEGER DEFAULT 0,
    department TEXT DEFAULT '',
    specifications TEXT DEFAULT '{}',
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE schedule_plans (
    id TEXT PRIMARY KEY,
    order_id TEXT NOT NULL,
    plan_type TEXT DEFAULT 'primary',
    total_time REAL DEFAULT 0,
    confidence_score REAL DEFAULT 0,
    risk_level TEXT DEFAULT 'medium',
    equipment_allocation TEXT DEFAULT '[]',
    operator_allocation TEXT DEFAULT '[]',
    delivery_prediction TEXT,
    optimization_strategy TEXT DEFAULT '',
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE schedule_progress (
    id INTEGER PRIMARY KEY,
    schedule_id TEXT NOT NULL,
    progress INTEGER NOT NULL,
    status TEXT NOT NULL,
    notes TEXT DEFAULT '',
    updated_by TEXT NOT NULL,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (schedule_id) REFERENCES schedules(id)
);

CREATE TABLE schedules (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    product_id TEXT NOT NULL,
    product_name TEXT NOT NULL,
    quantity INTEGER NOT NULL,
    start_time TEXT NOT NULL,
    end_time TEXT NOT NULL,
    status TEXT DEFAULT 'planned',
    priority TEXT DEFAULT 'medium',
    assigned_equipment TEXT DEFAULT '[]',
    assigned_personnel TEXT DEFAULT '[]',
    required_materials TEXT DEFAULT '[]',
    progress INTEGER DEFAULT 0,
    notes TEXT DEFAULT '',
    created_by TEXT NOT NULL,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE users (
    id TEXT PRIMARY KEY,
    usercode TEXT NOT NULL,
    username TEXT NOT NULL,
    password TEXT NOT NULL,
    role TEXT NOT NULL,
    department TEXT,
    email TEXT,
    active INTEGER DEFAULT 1,
    permissions TEXT DEFAULT '[]',
    last_login_at TEXT,
    last_active_at TEXT,
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL,
    has_signature INTEGER DEFAULT 0,
    signature_path TEXT,
    signature_base64 TEXT
);

CREATE TABLE warehouse_finished_products (
    id INTEGER PRIMARY KEY,
    product_code VARCHAR(50) NOT NULL,
    product_name VARCHAR(200) NOT NULL,
    batch_number VARCHAR(50),
    boxes_per_unit INTEGER DEFAULT 1,
    pieces_per_box INTEGER DEFAULT 1,
    total_pieces INTEGER,
    current_stock INTEGER DEFAULT 0,
    production_date DATE,
    expiry_date DATE,
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE warehouse_inventory_transactions (
    id INTEGER PRIMARY KEY,
    transaction_id VARCHAR(50) NOT NULL,
    item_type VARCHAR(20) NOT NULL,
    item_id INTEGER NOT NULL,
    transaction_type VARCHAR(20) NOT NULL,
    quantity INTEGER NOT NULL,
    unit VARCHAR(20) NOT NULL,
    qrcode VARCHAR(100),
    reason VARCHAR(100),
    operator_id INTEGER NOT NULL,
    order_number VARCHAR(50),
    supplier_info TEXT,
    customer_info TEXT,
    notes TEXT,
    transaction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (operator_id) REFERENCES users(id)
);

CREATE TABLE warehouse_materials (
    id INTEGER PRIMARY KEY,
    material_code VARCHAR(50) NOT NULL,
    material_name VARCHAR(200) NOT NULL,
    material_type VARCHAR(50) NOT NULL,
    supplier_code VARCHAR(50),
    supplier_name VARCHAR(200),
    unit VARCHAR(20) NOT NULL,
    safety_stock INTEGER DEFAULT 0,
    current_stock INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE warehouse_qrcodes (
    id INTEGER PRIMARY KEY,
    qrcode VARCHAR(100) NOT NULL,
    item_type VARCHAR(20) NOT NULL,
    item_id INTEGER NOT NULL,
    batch_info VARCHAR(100),
    quantity INTEGER,
    status VARCHAR(20) DEFAULT 'active',
    generated_by INTEGER NOT NULL,
    used_by INTEGER,
    used_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (used_by) REFERENCES users(id),
    FOREIGN KEY (generated_by) REFERENCES users(id)
);

CREATE INDEX idx_application_attachments_application_id ON application_attachments (application_id);
CREATE INDEX idx_applications_created_at ON applications (created_at);
CREATE INDEX idx_applications_current_stage ON applications (current_stage);
CREATE INDEX idx_applications_status ON applications (status);
CREATE INDEX idx_applications_user_id ON applications (user_id);
CREATE INDEX idx_approval_history_application_id ON approval_history (application_id);
CREATE INDEX idx_approval_history_approver_id ON approval_history (approver_id);
CREATE INDEX idx_equipment_area ON equipment (area);
CREATE INDEX idx_equipment_code ON equipment (code);
CREATE INDEX idx_equipment_responsible ON equipment (responsible);
CREATE INDEX idx_equipment_status ON equipment (status);
CREATE INDEX idx_equipment_health_assessment_date ON equipment_health (assessment_date);
CREATE INDEX idx_equipment_health_equipment_id ON equipment_health (equipment_id);
CREATE INDEX idx_equipment_health_level ON equipment_health (health_level);
CREATE INDEX idx_equipment_health_total_score ON equipment_health (total_score);
CREATE INDEX idx_equipment_health_history_assessment_date ON equipment_health_history (assessment_date);
CREATE INDEX idx_equipment_health_history_equipment_id ON equipment_health_history (equipment_id);
CREATE INDEX idx_equipment_maintenance_equipment_id ON equipment_maintenance (equipment_id);
CREATE INDEX idx_equipment_maintenance_status ON equipment_maintenance (status);
CREATE INDEX idx_factories_name ON factories (name);
CREATE INDEX idx_inventory_items_category ON inventory_items (category);
CREATE INDEX idx_inventory_items_created_by ON inventory_items (created_by);
CREATE INDEX idx_inventory_items_item_code ON inventory_items (item_code);
CREATE INDEX idx_inventory_items_name ON inventory_items (name);
CREATE INDEX idx_inventory_items_status ON inventory_items (status);
CREATE INDEX idx_inventory_label_templates_created_by ON inventory_label_templates (created_by);
CREATE INDEX idx_inventory_label_templates_is_default ON inventory_label_templates (is_default);
CREATE INDEX idx_inventory_operations_item_id ON inventory_operations (item_id);
CREATE INDEX idx_inventory_operations_operation_date ON inventory_operations (operation_date);
CREATE INDEX idx_inventory_operations_operation_type ON inventory_operations (operation_type);
CREATE INDEX idx_inventory_operations_operator ON inventory_operations (operator);
CREATE INDEX idx_ip_whitelist_created_by ON ip_whitelist(created_by);
CREATE INDEX idx_ip_whitelist_expires_at ON ip_whitelist(expires_at);
CREATE INDEX idx_ip_whitelist_group_name ON ip_whitelist(group_name);
CREATE INDEX idx_ip_whitelist_ip_address ON ip_whitelist(ip_address);
CREATE INDEX idx_ip_whitelist_is_active ON ip_whitelist(is_active);
CREATE INDEX idx_quality_report_files_file_type ON quality_report_files (file_type);
CREATE INDEX idx_quality_report_files_report_id ON quality_report_files (report_id);
CREATE INDEX idx_quality_reports_created_at ON quality_reports (created_at);
CREATE INDEX idx_quality_reports_report_number ON quality_reports (report_number);
CREATE INDEX idx_quality_reports_status ON quality_reports (status);
CREATE INDEX idx_quality_reports_test_date ON quality_reports (test_date);
CREATE INDEX idx_quality_reports_test_type ON quality_reports (test_type);
CREATE INDEX idx_quality_reports_uploaded_by ON quality_reports (uploaded_by);
CREATE INDEX idx_resources_status ON resources (status);
CREATE INDEX idx_resources_type ON resources (type);
CREATE INDEX idx_schedule_progress_schedule_id ON schedule_progress (schedule_id);
CREATE INDEX idx_schedules_created_by ON schedules (created_by);
CREATE INDEX idx_schedules_end_time ON schedules (end_time);
CREATE INDEX idx_schedules_priority ON schedules (priority);
CREATE INDEX idx_schedules_start_time ON schedules (start_time);
CREATE INDEX idx_schedules_status ON schedules (status);
CREATE INDEX idx_users_active ON users (active);
CREATE INDEX idx_users_role ON users (role);
CREATE INDEX idx_users_usercode ON users (usercode);
CREATE INDEX idx_warehouse_products_batch ON warehouse_finished_products(batch_number);
CREATE INDEX idx_warehouse_products_code ON warehouse_finished_products(product_code);
CREATE INDEX idx_warehouse_products_status ON warehouse_finished_products(status);
CREATE INDEX idx_warehouse_transactions_date ON warehouse_inventory_transactions(transaction_date);
CREATE INDEX idx_warehouse_transactions_id ON warehouse_inventory_transactions(transaction_id);
CREATE INDEX idx_warehouse_transactions_item ON warehouse_inventory_transactions(item_type, item_id);
CREATE INDEX idx_warehouse_transactions_operator ON warehouse_inventory_transactions(operator_id);
CREATE INDEX idx_warehouse_transactions_type ON warehouse_inventory_transactions(transaction_type);
CREATE INDEX idx_warehouse_materials_code ON warehouse_materials(material_code);
CREATE INDEX idx_warehouse_materials_status ON warehouse_materials(status);
CREATE INDEX idx_warehouse_materials_type ON warehouse_materials(material_type);
CREATE INDEX idx_warehouse_qrcodes_code ON warehouse_qrcodes(qrcode);
CREATE INDEX idx_warehouse_qrcodes_item ON warehouse_qrcodes(item_type, item_id);
CREATE INDEX idx_warehouse_qrcodes_status ON warehouse_qrcodes(status);

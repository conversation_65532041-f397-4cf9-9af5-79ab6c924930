{"timestamp": "2025-07-31T01:29:25.688Z", "totalTables": 49, "totalIndexes": 69, "tables": [{"name": "application_attachments", "columns": 8, "foreignKeys": 1}, {"name": "application_drafts", "columns": 8, "foreignKeys": 1}, {"name": "applications", "columns": 19, "foreignKeys": 1}, {"name": "approval_history", "columns": 10, "foreignKeys": 2}, {"name": "customer_file_attachments", "columns": 9, "foreignKeys": 1}, {"name": "customer_file_confirmations", "columns": 6, "foreignKeys": 2}, {"name": "customer_file_notifications", "columns": 7, "foreignKeys": 2}, {"name": "customer_file_number_reservations", "columns": 2, "foreignKeys": 0}, {"name": "customer_files", "columns": 14, "foreignKeys": 1}, {"name": "departments", "columns": 7, "foreignKeys": 0}, {"name": "equipment", "columns": 11, "foreignKeys": 0}, {"name": "equipment_capabilities", "columns": 8, "foreignKeys": 3}, {"name": "equipment_capacity", "columns": 11, "foreignKeys": 3}, {"name": "equipment_health", "columns": 16, "foreignKeys": 1}, {"name": "equipment_health_history", "columns": 12, "foreignKeys": 1}, {"name": "equipment_maintenance", "columns": 16, "foreignKeys": 1}, {"name": "equipment_operators", "columns": 5, "foreignKeys": 2}, {"name": "factories", "columns": 5, "foreignKeys": 0}, {"name": "file_management_attachments", "columns": 9, "foreignKeys": 1}, {"name": "file_management_customers", "columns": 13, "foreignKeys": 2}, {"name": "file_management_files", "columns": 14, "foreignKeys": 3}, {"name": "file_management_notifications", "columns": 7, "foreignKeys": 2}, {"name": "file_management_number_reservations", "columns": 2, "foreignKeys": 0}, {"name": "file_management_products", "columns": 11, "foreignKeys": 2}, {"name": "inventory_item_tags", "columns": 4, "foreignKeys": 2}, {"name": "inventory_items", "columns": 16, "foreignKeys": 1}, {"name": "inventory_label_templates", "columns": 8, "foreignKeys": 1}, {"name": "inventory_operations", "columns": 11, "foreignKeys": 2}, {"name": "inventory_records", "columns": 13, "foreignKeys": 1}, {"name": "inventory_tags", "columns": 6, "foreignKeys": 0}, {"name": "ip_whitelist", "columns": 12, "foreignKeys": 0}, {"name": "migrations", "columns": 4, "foreignKeys": 0}, {"name": "operator_skills", "columns": 10, "foreignKeys": 2}, {"name": "permission_templates", "columns": 7, "foreignKeys": 0}, {"name": "production_processes", "columns": 10, "foreignKeys": 1}, {"name": "products", "columns": 11, "foreignKeys": 0}, {"name": "quality_report_files", "columns": 9, "foreignKeys": 1}, {"name": "quality_report_number_reservations", "columns": 2, "foreignKeys": 0}, {"name": "quality_report_numbers", "columns": 2, "foreignKeys": 0}, {"name": "quality_reports", "columns": 16, "foreignKeys": 1}, {"name": "resources", "columns": 10, "foreignKeys": 0}, {"name": "schedule_plans", "columns": 12, "foreignKeys": 0}, {"name": "schedule_progress", "columns": 7, "foreignKeys": 1}, {"name": "schedules", "columns": 17, "foreignKeys": 0}, {"name": "users", "columns": 16, "foreignKeys": 0}, {"name": "warehouse_finished_products", "columns": 13, "foreignKeys": 0}, {"name": "warehouse_inventory_transactions", "columns": 16, "foreignKeys": 1}, {"name": "warehouse_materials", "columns": 12, "foreignKeys": 0}, {"name": "warehouse_qrcodes", "columns": 11, "foreignKeys": 2}]}